<?php
require 'includes/config.php'; 
require 'includes/main.php';

$sql = "SELECT id, title, short_description, image, category, created_at 
        FROM blog_posts 
        ORDER BY created_at DESC 
        LIMIT 3";
$stmt = $pdo->prepare($sql);
$stmt->execute();
$blogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Boat Shipping Quote | Safe Marine Transport Pricing - Safe Car Hauler</title>
    <!-- Meta -->
    <meta name="description" content="Get your custom quote for boat, yacht, or jet ski shipping with Safe Car Hauler. Nationwide marine transport you can trust." />
    <meta name="keywords" content="boat shipping quote, yacht transport cost, marine hauling price, jet ski delivery, Safe Car Hauler" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="index, follow" />

    <!-- Open Graph -->
    <meta property="og:title" content="Boat Shipping Quote | Safe Car Hauler" />
    <meta property="og:description" content="Receive your personalized marine transport quote for boats, yachts, and jet skis. Fully insured coast-to-coast delivery." />
    <meta property="og:image" content="https://safecarhauler.com/public/images/services/Boat-on-highway.jpg" />
    <meta property="og:url" content="https://safecarhauler.com/quote-boat" />
    <meta property="og:type" content="website" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Boat Shipping Quote | Coast-to-Coast Marine Delivery" />
    <meta name="twitter:description" content="Get a fast, accurate quote for shipping your boat or watercraft anywhere in the U.S. with Safe Car Hauler." />
    <meta name="twitter:image" content="https://safecarhauler.com/public/images/services/Boat-on-highway.jpg" />

    <link rel="canonical" href="https://safecarhauler.com/quote-boat" />
    <link rel="stylesheet" type="text/css" href="public/css/index.css" />
    <link rel="stylesheet" type="text/css" href="public/css/styles.css" />
    <link rel="stylesheet" type="text/css" href="public/css/components.css" />
    <link rel="stylesheet" type="text/css" href="public/css/main.css" />
    <link rel="stylesheet" type="text/css" href="public/css/quote.css" />

<!-- iubenda start -->
    <script type="text/javascript">
        var _iub = _iub || {}; 
        _iub.cons_instructions = _iub.cons_instructions || []; 
        _iub.cons_instructions.push(["init", {api_key: "ri0L77KZSPdst3FYVu0GB0GG0oQ59GS7"}]);
    </script>
    <script type="text/javascript" src="https://cdn.iubenda.com/cons/iubenda_cons.js" async></script>
    <script type="text/javascript">
    var _iub = _iub || [];
    _iub.csConfiguration = {"siteId":3966585,"cookiePolicyId":28502363,"lang":"en","storage":{"useSiteId":true}};
    </script>
    <script type="text/javascript" src="https://cs.iubenda.com/autoblocking/3966585.js"></script>
    <script type="text/javascript" src="//cdn.iubenda.com/cs/gpp/stub.js"></script>
    <script type="text/javascript" src="//cdn.iubenda.com/cs/iubenda_cs.js" charset="UTF-8" async></script>

<!-- iubenda end -->
</head>
<body>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
// Global error handler for debugging
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    console.error('Error message:', e.message);
    console.error('Error source:', e.filename, 'line:', e.lineno);
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
    e.preventDefault(); // Prevent the default browser behavior
});
</script>
<script>
// Initialize autocomplete for a specific input element
function initializeAutocomplete(inputElement) {
    if (!inputElement) {
        console.warn('Input element not found for autocomplete');
        return;
    }

    if (typeof google === 'undefined' || !google.maps || !google.maps.places) {
        console.error('Google Maps Places API not available');
        return;
    }

    try {
        const autocomplete = new google.maps.places.Autocomplete(inputElement, {
            types: ['(regions)'],
            componentRestrictions: { country: 'us' }
        });

        autocomplete.addListener('place_changed', function () {
            const place = autocomplete.getPlace();
            if (place.address_components) {
                let city = '';
                let state = '';
                let zip = '';

                place.address_components.forEach(component => {
                    const types = component.types;
                    if (types.includes('locality') || types.includes('postal_town')) {
                        city = component.long_name;
                    }
                    if (types.includes('administrative_area_level_1')) {
                        state = component.short_name;
                    }
                    if (types.includes('postal_code')) {
                        zip = component.long_name;
                    }
                });

                const formatted = `${city}, ${state} ${zip}, USA`;
                if (typeof $ !== 'undefined') {
                    $(inputElement).val(formatted);
                } else {
                    inputElement.value = formatted;
                }
                console.log('Autocomplete result:', formatted);
            }
        });

        console.log('Autocomplete initialized for:', inputElement.id);
    } catch (error) {
        console.error('Error initializing autocomplete:', error);
    }
}

// Global callback function for Google Maps API
function initGoogleMaps() {
    console.log('Google Maps API callback triggered');
    console.log('Google object:', typeof google);
    console.log('Google Maps:', typeof google.maps);
    console.log('Google Places:', typeof google.maps.places);

    // Check if all required APIs are loaded
    if (typeof google === 'undefined') {
        console.error('Google object not defined');
        return;
    }

    if (!google.maps) {
        console.error('Google Maps not loaded');
        return;
    }

    if (!google.maps.places) {
        console.error('Google Places not loaded');
        return;
    }

    console.log('All Google APIs loaded successfully');

    // Wait a bit to ensure DOM is ready
    setTimeout(function() {
        const fromInput = document.getElementById('transport_from');
        const toInput = document.getElementById('transport_to');

        console.log('DOM elements found:', {
            transport_from: !!fromInput,
            transport_to: !!toInput
        });

        if (fromInput) {
            console.log('Initializing autocomplete for transport_from');
            initializeAutocomplete(fromInput);
        }

        if (toInput) {
            console.log('Initializing autocomplete for transport_to');
            initializeAutocomplete(toInput);
        }
    }, 1000);
}
</script>
<script>
// Timeout to check if Google Maps loaded
setTimeout(function() {
    if (typeof google === 'undefined') {
        console.warn('Google Maps API failed to load within 10 seconds');
        // You can add fallback behavior here if needed
    }
}, 10000);
</script>
<script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDPqmvF0Uf9aR1N1hQZVSUyibk__vkaegk&libraries=places&callback=initGoogleMaps" onerror="console.error('Failed to load Google Maps script')"></script>
    <div class="fix-quote-centered-form">
    <div id="notification" class="notification" style="display: none;">Order Sent Successfully!</div>
        <a href="/"><img src="public/images/img_header_logo.svg" alt="Logo" class="fix-quote-logo"></a>
          <form id="transportForm" action="send-lead-boat" method="post" class="stackarrowdown">
            <div class="quote-1">
              <h2 class="getaninstant ui heading size-headings" style="margin-left: 20px;">
                <span> Get an instant Quote Now</span>
                <span class="getaninstant-span">or call <a href="tel:8778788008" class="getaninstant-span-call"> (*************</a></span>
              </h2>
<!-- Step 1: From/To form -->
<div class="step" id="step-1">
                <div class="input-group">
                  <div class="input-wrapper">
                    <img src="public/images/Vector.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_from" id="transport_from" class="from-input" placeholder="From (ZIP or City, State)" />
                  </div>
                  <div class="input-wrapper">
                    <img src="public/images/Vector-2.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_to" id="transport_to" class="to-input" placeholder="To (ZIP or City, State)" />
                  </div>
                </div>
                <label class="transoprt-type">Is Boat on a Trailer?</label>
                <div class="rowtransporttyp">
                <label class="ui checkbox">
                     <input type="radio" name="transport_type" value="Yes" />
                <div></div><span>Yes</span>
               </label>
                 <label class="ui checkbox">
                  <input type="radio" name="transport_type" value="No" />
                    <div></div><span>No</span>
                     </label>
                   </div>

                <button type="button" class="step-buttom" onclick="nextStep(2)">Boat Details</button>
              </div>

              <!-- Step 2: Vehicle details -->
              <div class="step" id="step-2" style="display: none;">
                <div class="input-group">
                <div class="select-custom">
            <select name="vehicle_year" id="vehicle_year" class="styled-select">
                <option value="" disabled selected>Boat year</option>
            </select>
            <span class="dropdown-icon"></span>
        </div>

        <!-- Brand Dropdown -->
        <div class="select-custom">
            <select name="vehicle_brand" id="vehicle_brand" class="styled-select" disabled>
                <option value="" disabled selected>Vessel Information</option>
            </select>
            <span class="dropdown-icon"></span>
        </div>

        <!-- Model Dropdown -->
        <div class="input-wrapper">
        <input type="text" name="vehicle_model" id="vehicle_model" class="from-input pac-target-input" placeholder="Boat Make & Model ">
        </div>
  
                </div>

                <div class="step-indicators">
                  <div class="indicator completed-step" id="step-indicator-1" onclick="goToStep(1)">
                    <span class="indicator-number">1</span>
                    <p>Destination</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator active-step" id="step-indicator-2">
                    <span class="indicator-number">2</span>
                    <p>Vehicle</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator" id="step-indicator-3">
                    <span class="indicator-number">3</span>
                    <p>Info</p>
                  </div>
                </div>
                <button type="button" class="step-buttom" onclick="nextStep(3)">Confirmation Details</button>
              </div>

              <!-- Step 3: Contact details -->
              <div class="step" id="step-3" style="display: none;">
                <div class="input-group">
                  <div class="input-wrapper">
                    <input type="email" name="email" id="email" class="info-input" placeholder="Your Email" />
                  </div>
                  <div class="select-custom">
                   <select name="available_date" id="available_date" class="styled-select">
                   <option value="" selected>Ship Date</option>
                   <option value="asap">As soon as possible</option>
                   <option value="2_weeks">Within 2 weeks</option>
                   <option value="30_days">Within 30 days</option>
                   <option value="more_than_30_days">More than 30 days</option>
                   </select>
                   <span class="dropdown-icon"></span>
                  </div>
                  <div class="input-wrapper">
                    <input type="text" name="name" id="name" class="info-input" placeholder="Your name" />
                  </div>
                  <div class="input-wrapper">
                    <input type="tel" name="phone" id="phone" class="info-input" placeholder="Your phone" />
                  </div>
                </div>
                <div class="step-indicators">
                  <div class="indicator completed-step" id="step-indicator-1" onclick="goToStep(1)">
                    <span class="indicator-number">1</span>
                    <p>Destination</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator completed-step" id="step-indicator-2"  onclick="goToStep(2)">
                    <span class="indicator-number">2</span>
                    <p>Vehicle</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator active-step" id="step-indicator-3">
                    <span class="indicator-number">3</span>
                    <p>Info</p>
                  </div>
                </div>
                <button type="submit" class="step-buttom" id="finishButton">Get Quote</button>
              </div>
              
            </div>
          </form>
    </div>
<script>
// initializeAutocomplete function moved above

// Google Maps API will be initialized via callback function
</script>
<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function () {
        setTimeout(function () {   
            let submitButton = document.getElementById("finishButton");
            let transportForm = document.getElementById("transportForm");

            if (submitButton && transportForm) {
                console.log("iubenda initialized:", submitButton, transportForm);

                _iub.cons_instructions.push(["load", {
                    submitElement: submitButton,
                    form: {
                    selector: transportForm,
                                map: {
                                    subject: {
                                        full_name: "name",  
                                        // first_name: "name",  
                                        email: "email",  
                                        phones: "phone"
                                    },
                                    preferences: {  
                                        transport_from: "transport_from",
                                        transport_to: "transport_to",
                                        transport_type: "transport_type",
                                        vehicle_year: "vehicle_year",
                                        vehicle_brand: "vehicle_brand",
                                        vehicle_model: "vehicle_model",
                                        available_date: "available_date"
                                    }
                                }
                            },
                            consent: {
                                legal_notices: [
                                    { identifier: "privacy_policy" },
                                    { identifier: "cookie_policy" },
                                    { identifier: "terms" }
                                ]
                            }
                        }]);
                    } else {
                        console.error("Error: Submit button or form not found.");
                    }
                }, 2000);
            });
</script>

<script defer src="public/js/send-boat.js"></script>
</body>
</html>
