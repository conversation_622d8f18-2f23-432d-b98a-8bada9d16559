// Google Maps API Initialization
// This file handles the proper initialization of Google Maps Places API

// Default ZIP codes for each state (fallback when no ZIP is provided)
const defaultStateZips = {
    'AL': '35203', 'AK': '99501', 'AZ': '85001', 'AR': '72201', 'CA': '90210',
    'CO': '80202', 'CT': '06103', 'DE': '19901', 'FL': '32301', 'GA': '30303',
    'HI': '96813', 'ID': '83702', 'IL': '60601', 'IN': '46204', 'IA': '50309',
    'KS': '66603', 'KY': '40601', 'LA': '70112', 'ME': '04330', 'MD': '21401',
    'MA': '02108', 'MI': '48933', 'MN': '55101', 'MS': '39201', 'MO': '65101',
    'MT': '59601', 'NE': '68501', 'NV': '89701', 'NH': '03301', 'NJ': '08608',
    'NM': '87501', 'NY': '10001', 'NC': '27601', 'ND': '58501', 'OH': '43215',
    'OK': '73102', 'OR': '97301', 'PA': '17101', 'RI': '02903', 'SC': '29201',
    'SD': '57501', 'TN': '37219', 'TX': '78701', 'UT': '84111', 'VT': '05602',
    'VA': '23219', 'WA': '98507', 'WV': '25301', 'WI': '53703', 'WY': '82001',
    'DC': '20001'
};

// Initialize autocomplete for a specific input element
function initializeAutocomplete(inputElement) {
    if (!inputElement) {
        console.warn('Input element not found for autocomplete initialization');
        return;
    }

    console.log('Initializing autocomplete for:', inputElement.id);
    
    const autocomplete = new google.maps.places.Autocomplete(inputElement, {
        types: ['(regions)'],
        componentRestrictions: { country: 'us' }
    });

    autocomplete.addListener('place_changed', function () {
        const place = autocomplete.getPlace();
        if (place.address_components) {
            let city = '';
            let state = '';
            let zip = '';

            place.address_components.forEach(component => {
                const types = component.types;
                if (types.includes('locality') || types.includes('postal_town')) {
                    city = component.long_name;
                }
                if (types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                }
                if (types.includes('postal_code')) {
                    zip = component.long_name;
                }
            });

            // If no ZIP code but we have state, use default ZIP for that state
            if (!zip && state && defaultStateZips[state]) {
                zip = defaultStateZips[state];
            }

            const formatted = `${city}, ${state} ${zip}, USA`;
            
            // Use jQuery if available, otherwise use vanilla JS
            if (typeof $ !== 'undefined') {
                $(inputElement).val(formatted);
            } else {
                inputElement.value = formatted;
            }
            
            console.log('Autocomplete result:', formatted);
        }
    });
}

// Global callback function for Google Maps API
function initGoogleMaps() {
    console.log('Google Maps API loaded successfully');
    
    // Initialize autocomplete for common input IDs
    const inputIds = ['transport_from', 'transport_to'];
    
    inputIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            initializeAutocomplete(element);
        }
    });
}

// Fallback initialization for cases where callback might not work
function checkAndInitGoogleMaps() {
    if (typeof google !== 'undefined' && typeof google.maps !== 'undefined' && typeof google.maps.places !== 'undefined') {
        console.log('Google Maps API available, initializing...');
        initGoogleMaps();
        return true;
    }
    return false;
}

// Try to initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for Google Maps API to load if not already loaded
    setTimeout(function() {
        if (!checkAndInitGoogleMaps()) {
            console.log('Google Maps API not ready yet, will wait for callback');
        }
    }, 1000);
});
