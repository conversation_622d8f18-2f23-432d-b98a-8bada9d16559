<?php
require 'includes/config.php'; 
require 'includes/main.php';

$sql = "SELECT id, title, short_description, image, category, created_at 
        FROM blog_posts 
        ORDER BY created_at DESC 
        LIMIT 3";
$stmt = $pdo->prepare($sql);
$stmt->execute();
$blogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Get a Free Car Shipping Quote | Safe Car Hauler</title>
    <!-- Meta -->
    <meta name="description" content="Request your free car shipping quote now with Safe Car Hauler. Instant, no-obligation pricing for nationwide door-to-door vehicle transport." />
    <meta name="keywords" content="car shipping quote, auto transport quote, vehicle shipping price, get car transport quote, Safe Car Hauler" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="index, follow" />

    <!-- Open Graph -->
    <meta property="og:title" content="Get a Free Car Shipping Quote | Safe Car Hauler" />
    <meta property="og:description" content="Instant quotes for safe, insured, coast-to-coast car shipping. Get your personalized auto transport pricing now." />
    <meta property="og:image" content="https://safecarhauler.com/public/images/services/big-rig-car-carrier-desert.jpg" />
    <meta property="og:url" content="https://safecarhauler.com/quote" />
    <meta property="og:type" content="website" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Free Car Shipping Quote | Safe Car Hauler" />
    <meta name="twitter:description" content="Request your free, instant car transport quote. Nationwide, door-to-door service with full insurance." />
    <meta name="twitter:image" content="https://safecarhauler.com/public/images/services/big-rig-car-carrier-desert.jpg" />

    <link rel="canonical" href="https://safecarhauler.com/quote" />
    <link rel="stylesheet" type="text/css" href="public/css/index.css" />
    <link rel="stylesheet" type="text/css" href="public/css/styles.css" />
    <link rel="stylesheet" type="text/css" href="public/css/components.css" />
    <link rel="stylesheet" type="text/css" href="public/css/main.css" />
    <link rel="stylesheet" type="text/css" href="public/css/quote.css" />
    <link rel="icon" type="image/x-icon" href="public/favicon.ico">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">


    <!-- iubenda start -->
    <script type="text/javascript">
        var _iub = _iub || {}; 
        _iub.cons_instructions = _iub.cons_instructions || []; 
        _iub.cons_instructions.push(["init", {api_key: "ri0L77KZSPdst3FYVu0GB0GG0oQ59GS7"}]);
    </script>
    <script type="text/javascript" src="https://cdn.iubenda.com/cons/iubenda_cons.js" async></script>
    <script type="text/javascript">
    var _iub = _iub || [];
    _iub.csConfiguration = {"siteId":3966585,"cookiePolicyId":28502363,"lang":"en","storage":{"useSiteId":true}};
    </script>
    <script type="text/javascript" src="https://cs.iubenda.com/autoblocking/3966585.js"></script>
    <script type="text/javascript" src="//cdn.iubenda.com/cs/gpp/stub.js"></script>
    <script type="text/javascript" src="//cdn.iubenda.com/cs/iubenda_cs.js" charset="UTF-8" async></script>

    <!-- iubenda end -->

</head>
<body>
<script>
// Initialize autocomplete for a specific input element
function initializeAutocomplete(inputElement) {
    const autocomplete = new google.maps.places.Autocomplete(inputElement, {
        types: ['(regions)'],
        componentRestrictions: { country: 'us' }
    });

    autocomplete.addListener('place_changed', function () {
        const place = autocomplete.getPlace();
        if (place.address_components) {
            let city = '';
            let state = '';
            let zip = '';

            place.address_components.forEach(component => {
                const types = component.types;
                if (types.includes('locality') || types.includes('postal_town')) {
                    city = component.long_name;
                }
                if (types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                }
                if (types.includes('postal_code')) {
                    zip = component.long_name;
                }
            });

            const formatted = `${city}, ${state} ${zip}, USA`;
            $(inputElement).val(formatted);
        }
    });
}

// Global callback function for Google Maps API
function initGoogleMaps() {
    console.log('Google Maps API loaded successfully');
    if (document.getElementById('transport_from')) {
        initializeAutocomplete(document.getElementById('transport_from'));
    }
    if (document.getElementById('transport_to')) {
        initializeAutocomplete(document.getElementById('transport_to'));
    }
}
</script>
<script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDPqmvF0Uf9aR1N1hQZVSUyibk__vkaegk&libraries=places&callback=initGoogleMaps"></script>
    <div class="fix-quote-centered-form"> 
    <div id="notification" class="notification" style="display: none;">Order Sent Successfully!</div>
        <a href="/"><img src="public/images/img_header_logo.svg" alt="Logo" class="fix-quote-logo"></a>
          <form id="transportForm" action="send-leads-motorcycle" method="post" class="stackarrowdown">
            <div class="quote-1">
              <h2 class="getaninstant ui heading size-headings" style="margin-left: 20px;">
                <span> Get an instant Quote Now</span>
                <span class="getaninstant-span">or call <a href="tel:8778788008" class="getaninstant-span-call"> (*************</a></span>
              </h2>

              <!-- Step 1: From/To form -->
              <div class="step" id="step-1">
                <div class="input-group">
                  <div class="input-wrapper">
                    <img src="public/images/Vector.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_from" id="transport_from" class="from-input" placeholder="From (ZIP or City, State)" />
                  </div>
                  <div class="input-wrapper">
                    <img src="public/images/Vector-2.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_to" id="transport_to" class="to-input" placeholder="To (ZIP or City, State)" />
                  </div>
                </div>
                <label class="transoprt-type">Transport Type</label>
                <div class="rowtransporttyp">
                <label class="ui checkbox">
                     <input type="radio" name="transport_type" value="Open" />
                <div></div><span>Open</span>
               </label>
                 <label class="ui checkbox">
                  <input type="radio" name="transport_type" value="Enclosed" />
                    <div></div><span>Enclosed</span>
                     </label>
                   </div>
                   <div class="step-indicators">
                  <div class="indicator active-step" id="step-indicator-1" onclick="goToStep(1)">
                    <span class="indicator-number">1</span>
                    <p>Destination</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator" id="step-indicator-2">
                    <span class="indicator-number">2</span>
                    <p>Vehicle</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator" id="step-indicator-3">
                    <span class="indicator-number">3</span>
                    <p>Info</p>
                  </div>
                </div>
                <button type="button" class="step-buttom" onclick="nextStep(2)">Vehicle Details</button>
              </div>

              <!-- Step 2: Vehicle details -->
                <div class="step" id="step-2" style="display: none;">
                <div class="input-group">
                <div class="select-custom">
            <select name="vehicle_year" id="vehicle_year" class="styled-select" required>
                <option value="" disabled selected>Vehicle year</option>
            </select>
            <span class="dropdown-icon"></span>
            </div>

        <!-- Brand Dropdown -->
        <div class="select-custom">
            <div class="input-wrapper">
                <input type="text" name="vehicle_brand" id="vehicle_brand" class="from-input pac-target-input" placeholder="Motorcycle Make" />
            </div>
        </div>

        <!-- Model Dropdown -->
        <div class="select-custom">
         <div class="input-wrapper">
                <input type="text" name="vehicle_model" id="vehicle_model" class="from-input pac-target-input" placeholder="Motorcycle Model" />
            </div>
        </div>
                </div>
                <label class="vehicle-type">Is it operable?</label>
                <div class="rowtransporttyp">
                <label class="ui checkbox">
                     <input type="radio" name="vehicle_operable" value="yes" required />
                <div></div><span>Yes</span>
               </label>
                 <label class="ui checkbox">
                  <input type="radio" name="vehicle_operable" value="no" />
                    <div></div><span>No</span>
                     </label>
                   </div>
                <div class="step-indicators">
                  <div class="indicator completed-step" id="step-indicator-1" onclick="goToStep(1)">
                    <span class="indicator-number">1</span>
                    <p>Destination</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator active-step" id="step-indicator-2">
                    <span class="indicator-number">2</span>
                    <p>Vehicle</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator" id="step-indicator-3">
                    <span class="indicator-number">3</span>
                    <p>Info</p>
                  </div>
                </div>
                <button type="button" class="step-buttom" onclick="nextStep(3)">Confirmation Details</button>
              </div>

              <!-- Step 3: Contact details -->
              <div class="step" id="step-3" style="display: none;">
                <div class="input-group">
                  <div class="input-wrapper">
                    <input type="email" name="email" id="email" class="info-input" placeholder="Your Email" />
                  </div>
                <div class="input-wrapper">
                    <input type="text" name="available_date" id="available_date" class="info-input" placeholder="Select Available Date" readonly data-cmp-ab="2" required />
                  </div>
                  <div class="input-wrapper">
                    <input type="text" name="name" id="name" class="info-input" placeholder="Your name" />
                  </div>
                  <div class="input-wrapper">
                    <input type="tel" name="phone" id="phone" class="info-input" placeholder="Your phone" />
                  </div>
                </div>
                <div class="step-indicators">
                  <div class="indicator completed-step" id="step-indicator-1" onclick="goToStep(1)">
                    <span class="indicator-number">1</span>
                    <p>Destination</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator completed-step" id="step-indicator-2"  onclick="goToStep(2)">
                    <span class="indicator-number">2</span>
                    <p>Vehicle</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator active-step" id="step-indicator-3">
                    <span class="indicator-number">3</span>
                    <p>Info</p>
                  </div>
                </div>
                <button type="submit" class="step-buttom" id="finishButton">Get Quote</button>
              </div>
              
            </div>
          </form>
    </div>
<script>
// initializeAutocomplete function moved above

// Google Maps API will be initialized via callback function
</script>
<script>
  // Initialize datepicker with custom styling
  $(function() {
    // Set the form action to send-leads
    const transportForm = document.getElementById('transportForm');
    if (transportForm && (!transportForm.getAttribute('action') || transportForm.getAttribute('action') === '')) {
      transportForm.setAttribute('action', 'send-lead-motorcycle');
    }

    $("#available_date").datepicker({
      dateFormat: 'mm/dd/yy',
      minDate: 0, // Prevent selecting dates in the past
      showOtherMonths: true,
      selectOtherMonths: true,
      changeMonth: true,
      changeYear: true,
      yearRange: 'c:c+1', // Current year to next year
      beforeShow: function(input, inst) {
        // Add custom class to the datepicker
        setTimeout(function() {
          inst.dpDiv.addClass('custom-datepicker');
        }, 0);
      }
    });

    // Apply custom styling to the datepicker
    $("<style>")
      .prop("type", "text/css")
      .html(`
        .custom-datepicker {
          border: 1px solid #CDA565 !important;
          border-radius: 10px !important;
          font-family: 'Nohemi', sans-serif !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        }
        .custom-datepicker .ui-datepicker-header {
          background: #b68544 !important;
          color: #ffffff !important;
          border: none !important;
          border-radius: 8px 8px 0 0 !important;
        }
        .custom-datepicker .ui-datepicker-calendar thead th {
          color: #46351A !important;
          font-weight: 600 !important;
        }
        .custom-datepicker .ui-state-default {
          background: #F3F1F5 !important;
          border: 1px solid #E5E5E5 !important;
          color: #333333 !important;
          text-align: center !important;
        }
        .custom-datepicker .ui-state-active,
        .custom-datepicker .ui-state-hover {
          background: #CDA565 !important;
          color: #ffffff !important;
          border-color: #b68544 !important;
        }
      `)
      .appendTo("head");
  });
</script>

<script src="public/js/send-motorcycle.js"></script>
<!-- ibunde -->
<script type="text/javascript">
              document.addEventListener("DOMContentLoaded", function () {
                  setTimeout(function () {  
                      let submitButton = document.getElementById("finishButton");
                      let transportForm = document.getElementById("transportForm");

                      if (submitButton && transportForm) {
                          console.log("iubenda initialized with:", submitButton, transportForm);
                          _iub.cons_instructions.push(["load", {
                              submitElement: submitButton,
                              form: {
                                  selector: transportForm,
                                  map: {
                                      subject: {
                                          email: "email"
                                      }
                                  }
                              },
                              consent: {
                                  legal_notices: [{
                                      identifier: "privacy_policy",
                                  }]
                              }
                          }]);
                      } else {
                          console.error("Submit button or form not found.");
                      }
                  }, 2000);
              });
          </script>
<!-- ibunde End -->
</body>
</html>
