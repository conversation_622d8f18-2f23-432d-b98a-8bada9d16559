
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">

<div id="notification" class="notification" style="display: none;">Order Sent Successfully!</div>
<!-- Step 1: From/To form -->
<div class="step" id="step-1">
                <div class="input-group">
                  <div class="input-wrapper">
                    <img src="public/images/Vector.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_from" id="transport_from" class="from-input" placeholder="From (ZIP or City, State)" required />
                  </div>
                  <div class="input-wrapper">
                    <img src="public/images/Vector-2.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_to" id="transport_to" class="to-input" placeholder="To (ZIP or City, State)" required />
                  </div>
                </div>
                <div class="rowtransporttyp">
                </div>

                <button type="button" class="step-buttom" onclick="nextStep(2)">Vehicle Details</button>
              </div>

              <!-- Step 2: Vehicle details -->
              <div class="step" id="step-2" style="display: none;">
                <div class="input-group">
                <div class="select-custom">
            <select name="vehicle_year" id="vehicle_year" class="styled-select" required>
                <option value="" disabled selected>Vehicle year</option>
            </select>
            <span class="dropdown-icon"></span>
        </div>

        <!-- Brand Dropdown -->
        <div class="input-wrapper">
        <input type="text" name="vehicle_brand" id="vehicle_brand" class="from-input pac-target-input" placeholder="Vehicle Make" required>
        </div>

        <!-- Model Dropdown -->
        <div class="input-wrapper">
        <input type="text" name="vehicle_model" id="vehicle_model" class="from-input pac-target-input" placeholder="Vehicle Model" required>
        </div>
                </div>
                <label class="vehicle-type">Is it operable?</label>
                <div class="rowtransporttyp">
                <label class="ui checkbox">
                     <input type="radio" name="vehicle_operable" value="yes" checked />
                <div></div><span>Yes</span>
               </label>
                 <label class="ui checkbox">
                  <input type="radio" name="vehicle_operable" value="no" />
                    <div></div><span>No</span>
                     </label>
                   </div>
                <button type="button" class="step-buttom" onclick="nextStep(3)">Confirmation Details</button>
              </div>

              <!-- Step 3: Contact details -->
              <div class="step" id="step-3" style="display: none;">
                <div class="input-group">
                  <div class="input-wrapper">
                    <input type="email" name="email" id="email" class="info-input" placeholder="Your Email" required />
                  </div>
                  <div class="input-wrapper">
                    <input type="text" name="available_date" id="available_date" class="info-input" placeholder="Available Date" required />
                  </div>
                  <div class="input-wrapper">
                    <input type="text" name="name" id="name" class="info-input" placeholder="Your name" required />
                  </div>
                  <div class="input-wrapper">
                    <input type="tel" name="phone" id="phone" class="info-input" placeholder="Your phone" required />
                  </div>
                </div>
               <div class="step-indicators">
                  <div class="indicator completed-step" id="step-indicator-1" onclick="goToStep(1)">
                    <span class="indicator-number">1</span>
                    <p>Destination</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator completed-step" id="step-indicator-2"  onclick="goToStep(2)">
                    <span class="indicator-number">2</span>
                    <p>Vehicle</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator active-step" id="step-indicator-3">
                    <span class="indicator-number">3</span>
                    <p>Info</p>
                  </div>
                </div>
                <button type="submit" class="step-buttom" id="finishButton">Get Quote</button>
              </div>
              
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function initializeAutocomplete(inputElement) {
    const autocomplete = new google.maps.places.Autocomplete(inputElement, {
        types: ['(regions)'], 
        componentRestrictions: { country: 'us' } 
    });

    autocomplete.addListener('place_changed', function () {
        const place = autocomplete.getPlace();
        if (place.address_components) {
            let city = '';
            let state = '';
            let zip = '';

            place.address_components.forEach(component => {
                const types = component.types;
                if (types.includes('locality') || types.includes('postal_town')) {
                    city = component.long_name;
                }
                if (types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                }
                if (types.includes('postal_code')) {
                    zip = component.long_name;
                }
            });

            const formatted = `${city}, ${state} ${zip}, USA`;
            $(inputElement).val(formatted);
        }
    });
}

// Google Maps API will be initialized via callback function

// Form submission handling
document.addEventListener("DOMContentLoaded", function () {
    const form = document.getElementById('transportForm');
    const notification = document.getElementById('notification');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            const submitButton = document.getElementById('finishButton');
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';

            // Collect form data
            const formData = new FormData(form);
            formData.append('vehicle_type', 'Heavy Equipment'); // Add vehicle type for heavy Heavy Equipment

            // Send AJAX request
            fetch('send-lead-heavy.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect immediately on success
                    window.location.href = data.redirect;
                } else {
                    // Reset button on error without showing notification
                    submitButton.disabled = false;
                    submitButton.textContent = 'Get Quote';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                notification.textContent = 'Error sending form. Please try again.';
                notification.style.display = 'block';
                notification.style.backgroundColor = '#f44336';
                
                // Reset button
                submitButton.disabled = false;
                submitButton.textContent = 'Get Quote';
            });
        });
    }
});

// iubenda consent handling
document.addEventListener("DOMContentLoaded", function () {
    setTimeout(function () {   
        let submitButton = document.getElementById("finishButton");
        let transportForm = document.getElementById("transportForm");

        if (submitButton && transportForm) {
            console.log("iubenda initialized:", submitButton, transportForm);

            _iub.cons_instructions.push(["load", {
                submitElement: submitButton,
                form: {
                    selector: transportForm,
                    map: {
                        subject: {
                            full_name: "name",
                            email: "email",
                            phones: "phone"
                        },
                        preferences: {
                            transport_from: "transport_from",
                            transport_to: "transport_to",
                            vehicle_year: "vehicle_year",
                            vehicle_brand: "vehicle_brand",
                            vehicle_model: "vehicle_model",
                            available_date: "available_date"
                        }
                    }
                },
                consent: {
                    legal_notices: [
                        { identifier: "privacy_policy" },
                        { identifier: "cookie_policy" },
                        { identifier: "terms" }
                    ]
                }
            }]);
        } else {
            console.error("Error: Submit button or form not found.");
        }
    }, 2000);
});
</script>
<script>
  // Initialize datepicker with custom styling
  $(function() {
    $("#available_date").datepicker({
      dateFormat: 'mm/dd/yy',
      minDate: 0, // Prevent selecting dates in the past
      showOtherMonths: true,
      selectOtherMonths: true,
      changeMonth: true,
      changeYear: true,
      yearRange: 'c:c+1', // Current year to next year
      beforeShow: function(input, inst) {
        // Add custom class to the datepicker
        setTimeout(function() {
          inst.dpDiv.addClass('custom-datepicker');
        }, 0);
      }
    });

    $("<style>")
      .prop("type", "text/css")
      .html(`
        .custom-datepicker {
          border: 1px solid #CDA565 !important;
          border-radius: 10px !important;
          font-family: 'Nohemi', sans-serif !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        }
        .custom-datepicker .ui-datepicker-header {
          background: #b68544 !important;
          color: #ffffff !important;
          border: none !important;
          border-radius: 8px 8px 0 0 !important;
        }
        .custom-datepicker .ui-datepicker-calendar thead th {
          color: #46351A !important;
          font-weight: 600 !important;
        }
        .custom-datepicker .ui-state-default {
          background: #F3F1F5 !important;
          border: 1px solid #EAD4B9 !important;
          color: #46351A !important;
          text-align: center !important;
        }
        .custom-datepicker .ui-state-hover {
          background: #EBE2D8 !important;
          color: #222A31 !important;
        }
        .custom-datepicker .ui-state-active {
          background: #CDA565 !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }
        .custom-datepicker .ui-datepicker-today .ui-state-default {
          border: 2px solid #b58544 !important;
        }
        .custom-datepicker .ui-datepicker-prev,
        .custom-datepicker .ui-datepicker-next {
          background: #b58544 !important;
          border: none !important;
          cursor: pointer !important;
        }
        .custom-datepicker .ui-datepicker-prev span,
        .custom-datepicker .ui-datepicker-next span {
          filter: brightness(0) invert(1) !important;
        }
      `)
      .appendTo("head");
  });

  // Function to validate fields and highlight empty ones
  function validateFields(fieldIds) {
    let allValid = true;

    fieldIds.forEach(function(fieldId) {
      const field = document.getElementById(fieldId);
      const isRadio = field.type === 'radio';

      if (isRadio) {
        const isChecked = document.querySelector(`input[name="${field.name}"]:checked`);
        if (!isChecked) {
          // Highlight radio group container
          document.querySelector(`input[name="${field.name}"]`).closest(".rowtransporttyp").classList.add("radio-error");
          allValid = false;
        } else {
          document.querySelector(`input[name="${field.name}"]`).closest(".rowtransporttyp").classList.remove("radio-error");
        }
      } else {
        // For regular inputs
        if (field.value.trim() === '') {
          // Add error class to highlight the field
          field.closest(".input-wrapper").classList.add("input-error");
          allValid = false;
        } else {
          field.closest(".input-wrapper").classList.remove("input-error");
        }
      }
    });

    return allValid;
  }

  // Add validation to the date field specifically
  document.getElementById('available_date').addEventListener('change', function() {
    if (this.value.trim() === '') {
      this.closest(".input-wrapper").classList.add("input-error");
    } else {
      this.closest(".input-wrapper").classList.remove("input-error");
    }
  });

</script>
