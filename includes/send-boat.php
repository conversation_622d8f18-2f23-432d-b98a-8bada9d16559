
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<script>
// Global error handler for debugging
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    console.error('Error message:', e.message);
    console.error('Error source:', e.filename, 'line:', e.lineno);
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
    e.preventDefault(); // Prevent the default browser behavior
});
</script>

<div id="notification" class="notification" style="display: none;">Order Sent Successfully!</div>
<!-- Step 1: From/To form -->
<form id="transportForm" action="" method="post" class="stackarrowdown">
<div class="step" id="step-1">
                <div class="input-group">
                  <div class="input-wrapper">
                    <img src="public/images/Vector.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_from"  id="transport_from" class="from-input" placeholder="From (ZIP or City, State)" />
                  </div>
                  <div class="input-wrapper">
                    <img src="public/images/Vector-2.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_to" id="transport_to" class="to-input" placeholder="To (ZIP or City, State)" />
                  </div>
                </div>
                <label class="transoprt-type">Is Boat on a Trailer?</label>
                <div class="rowtransporttyp">
                <label class="ui checkbox">
                     <input type="radio" name="transport_type" value="Yes" />
                <div></div><span>Yes</span>
               </label>
                 <label class="ui checkbox">
                  <input type="radio" name="transport_type" value="No" />
                    <div></div><span>No</span>
                     </label>
                   </div>

                <button type="button" class="step-buttom" onclick="nextStep(2)">Boat Details</button>
              </div>

              <!-- Step 2: Vehicle details -->
              <div class="step" id="step-2" style="display: none;">
                <div class="input-group">
                <div class="select-custom">
            <select name="vehicle_year" id="vehicle_year" class="styled-select">
                <option value="" disabled selected>Boat year</option>
            </select>
            <span class="dropdown-icon"></span>
        </div>

        <!-- Brand Dropdown -->
        <div class="select-custom">
            <select name="vehicle_brand" id="vehicle_brand" class="styled-select" disabled>
                <option value="" disabled selected>Vessel Information</option>
                
            </select>
            <span class="dropdown-icon"></span>
        </div>

        <!-- Model Dropdown -->
        <div class="input-wrapper">
        <input type="text" name="vehicle_model" id="vehicle_model" class="from-input pac-target-input" placeholder="Boat Make & Model ">
        </div>
  
                </div>

                <button type="button" class="step-buttom" onclick="nextStep(3)">Confirmation Details</button>
              </div>

              <!-- Step 3: Contact details -->
              <div class="step" id="step-3" style="display: none;">
                <div class="input-group">
                  <div class="input-wrapper">
                    <input type="email" name="email" id="email" class="info-input" placeholder="Your Email" />
                  </div>
                  <div class="input-wrapper" style="display: none;">
                    <input type="hidden" name="transport_type" id="transport_type" value="Yes">
                  </div>
                  <div class="input-wrapper">
                    <input type="text" name="available_date" id="available_date" class="info-input" placeholder="Ship Date" readonly required />
                  </div>
                  <div class="input-wrapper">
                    <input type="text" name="name" id="name" class="info-input" placeholder="Your name" />
                  </div>
                  <div class="input-wrapper">
                    <input type="tel" name="phone" id="phone" class="info-input" placeholder="Your phone" />
                  </div>
                </div>
                <div class="step-indicators">
                  <div class="indicator completed-step" id="step-indicator-1" onclick="goToStep(1)">
                    <span class="indicator-number">1</span>
                    <p>Destination</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator completed-step" id="step-indicator-2"  onclick="goToStep(2)">
                    <span class="indicator-number">2</span>
                    <p>Vehicle</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator active-step" id="step-indicator-3">
                    <span class="indicator-number">3</span>
                    <p>Info</p>
                  </div>
                </div>
                <button type="submit" class="step-buttom" id="finishButton">Get Quote</button>
              </div>
              
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Initialize autocomplete for a specific input element
function initializeAutocomplete(inputElement) {
    if (!inputElement) {
        console.warn('Input element not found for autocomplete');
        return;
    }

    if (typeof google === 'undefined' || !google.maps || !google.maps.places) {
        console.error('Google Maps Places API not available');
        return;
    }

    try {
        const autocomplete = new google.maps.places.Autocomplete(inputElement, {
            types: ['(regions)'],
            componentRestrictions: { country: 'us' }
        });

        autocomplete.addListener('place_changed', function () {
            const place = autocomplete.getPlace();
            if (place.address_components) {
                let city = '';
                let state = '';
                let zip = '';

                place.address_components.forEach(component => {
                    const types = component.types;
                    if (types.includes('locality') || types.includes('postal_town')) {
                        city = component.long_name;
                    }
                    if (types.includes('administrative_area_level_1')) {
                        state = component.short_name;
                    }
                    if (types.includes('postal_code')) {
                        zip = component.long_name;
                    }
                });

                const formatted = `${city}, ${state} ${zip}, USA`;
                if (typeof $ !== 'undefined') {
                    $(inputElement).val(formatted);
                } else {
                    inputElement.value = formatted;
                }
                console.log('Autocomplete result:', formatted);
            }
        });

        console.log('Autocomplete initialized for:', inputElement.id);
    } catch (error) {
        console.error('Error initializing autocomplete:', error);
    }
}

// Global callback function for Google Maps API
function initGoogleMaps() {
    console.log('Google Maps API callback triggered');
    console.log('Google object:', typeof google);
    console.log('Google Maps:', typeof google.maps);
    console.log('Google Places:', typeof google.maps.places);

    // Check if all required APIs are loaded
    if (typeof google === 'undefined') {
        console.error('Google object not defined');
        return;
    }

    if (!google.maps) {
        console.error('Google Maps not loaded');
        return;
    }

    if (!google.maps.places) {
        console.error('Google Places not loaded');
        return;
    }

    console.log('All Google APIs loaded successfully');

    // Wait a bit to ensure DOM is ready
    setTimeout(function() {
        const fromInput = document.getElementById('transport_from');
        const toInput = document.getElementById('transport_to');

        console.log('DOM elements found:', {
            transport_from: !!fromInput,
            transport_to: !!toInput
        });

        if (fromInput) {
            console.log('Initializing autocomplete for transport_from');
            initializeAutocomplete(fromInput);
        }

        if (toInput) {
            console.log('Initializing autocomplete for transport_to');
            initializeAutocomplete(toInput);
        }
    }, 1000);
}

// Timeout to check if Google Maps loaded
setTimeout(function() {
    if (typeof google === 'undefined') {
        console.warn('Google Maps API failed to load within 10 seconds');
        // You can add fallback behavior here if needed
    }
}, 10000);
</script>
<script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDPqmvF0Uf9aR1N1hQZVSUyibk__vkaegk&libraries=places&callback=initGoogleMaps" onerror="console.error('Failed to load Google Maps script')"></script>

<script>
  // Initialize datepicker with custom styling
  $(function() {
    $("#available_date").datepicker({
      dateFormat: 'mm/dd/yy',
      minDate: 0, // Prevent selecting dates in the past
      showOtherMonths: true,
      selectOtherMonths: true,
      changeMonth: true,
      changeYear: true,
      yearRange: 'c:c+1', // Current year to next year
      beforeShow: function(input, inst) {
        // Add custom class to the datepicker
        setTimeout(function() {
          inst.dpDiv.addClass('custom-datepicker');
        }, 0);
      }
    });

    $("<style>")
      .prop("type", "text/css")
      .html(`
        .custom-datepicker {
          border: 1px solid #CDA565 !important;
          border-radius: 10px !important;
          font-family: 'Nohemi', sans-serif !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        }
        .custom-datepicker .ui-datepicker-header {
          background: #b68544 !important;
          color: #ffffff !important;
          border: none !important;
          border-radius: 8px 8px 0 0 !important;
        }
        .custom-datepicker .ui-datepicker-calendar thead th {
          color: #46351A !important;
          font-weight: 600 !important;
        }
        .custom-datepicker .ui-state-default {
          background: #F3F1F5 !important;
          border: 1px solid #EAD4B9 !important;
          color: #46351A !important;
          text-align: center !important;
        }
        .custom-datepicker .ui-state-hover {
          background: #EBE2D8 !important;
          color: #222A31 !important;
        }
        .custom-datepicker .ui-state-active {
          background: #CDA565 !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }
        .custom-datepicker .ui-datepicker-today .ui-state-default {
          border: 2px solid #b58544 !important;
        }
        .custom-datepicker .ui-datepicker-prev,
        .custom-datepicker .ui-datepicker-next {
          background: #b58544 !important;
          border: none !important;
          cursor: pointer !important;
        }
        .custom-datepicker .ui-datepicker-prev span,
        .custom-datepicker .ui-datepicker-next span {
          filter: brightness(0) invert(1) !important;
        }
      `)
      .appendTo("head");
  });

  // Function to validate fields and highlight empty ones
  function validateFields(fieldIds) {
    let allValid = true;

    fieldIds.forEach(function(fieldId) {
      const field = document.getElementById(fieldId);
      const isRadio = field.type === 'radio';

      if (isRadio) {
        const isChecked = document.querySelector(`input[name="${field.name}"]:checked`);
        if (!isChecked) {
          // Highlight radio group container
          document.querySelector(`input[name="${field.name}"]`).closest(".rowtransporttyp").classList.add("radio-error");
          allValid = false;
        } else {
          document.querySelector(`input[name="${field.name}"]`).closest(".rowtransporttyp").classList.remove("radio-error");
        }
      } else {
        // For regular inputs
        if (field.value.trim() === '') {
          // Add error class to highlight the field
          field.closest(".input-wrapper").classList.add("input-error");
          allValid = false;
        } else {
          field.closest(".input-wrapper").classList.remove("input-error");
        }
      }
    });

    return allValid;
  }

  // Add validation to the date field specifically
  document.getElementById('available_date').addEventListener('change', function() {
    if (this.value.trim() === '') {
      this.closest(".input-wrapper").classList.add("input-error");
    } else {
      this.closest(".input-wrapper").classList.remove("input-error");
    }
  });

</script>

<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function () {
        setTimeout(function () {   
            let submitButton = document.getElementById("finishButton");
            let transportForm = document.getElementById("transportForm");

            if (submitButton && transportForm) {
                console.log("iubenda initialized:", submitButton, transportForm);

                _iub.cons_instructions.push(["load", {
                    submitElement: submitButton,
                    form: {
                    selector: transportForm,
                                map: {
                                    subject: {
                                        full_name: "name",  
                                        // first_name: "name",  
                                        email: "email",  
                                        phones: "phone"
                                    },
                                    preferences: {  
                                        transport_from: "transport_from",
                                        transport_to: "transport_to",
                                        transport_type: "transport_type",
                                        vehicle_year: "vehicle_year",
                                        vehicle_brand: "vehicle_brand",
                                        vehicle_model: "vehicle_model",
                                        vehicle_type: "vehicle_type",
                                        available_date: "available_date"
                                    }
                                }
                            },
                            consent: {
                                legal_notices: [
                                    { identifier: "privacy_policy" },
                                    { identifier: "cookie_policy" },
                                    { identifier: "terms" }
                                ]
                            }
                        }]);
                    } else {
                        console.error("Error: Submit button or form not found.");
                    }
                }, 2000);
            });
</script>

